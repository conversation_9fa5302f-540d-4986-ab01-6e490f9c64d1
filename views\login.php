<?php
$fileName = pathinfo(__FILE__)['filename'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js" integrity="sha384-k6d4wzSIapyDyv1kpU366/PK5hCdSbCRGRCMv+eplOQJWyd1fbcAu9OCUj5zNLiq" crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="/css/views/<?= $fileName ?>.css">
    <title>Login</title>
</head>
<body>
    <div class="containers d-flex justify-content-center align-items-center vh-100">
        <form class="p-3 rounded shadow-lg">
            <div class="row mb-3">
                <div id="login" class="col-6 text-center p-2 fw-bold fs-3 rounded" style="background-color: rgb(36, 41, 102); color: white;">Login</div>
                <div id="register" class="col-6 text-center p-2 fw-bold fs-3 rounded">Register</div>
            </div>

            <!-- Changing here -->
            <div class="form-body">
                <div class="form-group mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
                </div>
                <div class="form-group mb-3">
                    <label for="pass" class="form-label">Password</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="pass" name="pass" placeholder="Enter your password" required>
                        <span class="input-group-text see-pass" style="cursor: pointer;"><i class="bi bi-eye-fill"></i></span>
                    </div>
                </div>
            </div>
            <!-- End of changing -->
            
            <div id="error-message" class="alert alert-danger" style="display: none;"></div>
            <button type="submit" class="btn btn-secondary w-100 my-4 fs-4">Login</button>
        </form>
    </div>

    <script src="/js/shared/submit.js"></script>
    <script src="/js/views/<?= $fileName ?>.js"></script>
</body>
</html>