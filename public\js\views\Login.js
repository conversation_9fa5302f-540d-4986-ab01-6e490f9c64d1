let tabStatus = true // true = LOGIN mode; false = REGISTER mode
let loginText = $(".form-body").html()

$(document).ready(() => {
    history.pushState(null, '', '/login')
})

// Switch to the LOGIN mode
$("#login").on("click", () => {
    $("#register").removeAttr("style")
    $("form").removeAttr("enctype")
    
    if (tabStatus === false) {
        let formBody = $("<div class='form-body'>").html(loginText)
        
        $(".form-body").replaceWith(formBody)
        $("button[type='submit']").text("Login")
        $("#login").css({
            "backgroundColor": "rgb(36, 41, 102)",
            "color": "white"
        })

        tabStatus = true
    }
})

// Switch to the REGISTER mode
$("#register").on("click", () => {
    $("#login").removeAttr("style")
    $("form").attr("enctype", "multipart/form-data")

    if (tabStatus === true) {
        let formBody = $("<div class='form-body'>").html(`
            <div class="form-group mb-3">
                <label for="email" class="form-label">Email<span style="color: red;">*</span></label>
                <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
            </div>
            <div class="form-group mb-3">
                <label for="username" class="form-label">Username<span style="color: red;">*</span></label>
                <input type="text" class="form-control" id="username" name="username" placeholder="Enter your username" required>
            </div>
            <div class="form-group mb-3">
                <label for="pass" class="form-label">Password<span style="color: red;">*</span></label>
                <div class="input-group">
                    <input type="password" class="form-control" id="pass" name="pass" placeholder="Enter your password" required>
                    <span class="input-group-text see-pass" style="cursor: pointer;"><i class="bi bi-eye-fill"></i></span>
                </div>
                <div id="check-pass" class="fs-6 fst-italic text-danger" style="display: none;">Password must be longer than 6 characters</div>
            </div>
            <div class="form-group mb-3">
                <label for="pass-again" class="form-label">Enter password again<span style="color: red;">*</span></label>
                <div class="input-group">
                    <input type="password" class="form-control" id="pass-again" placeholder="Enter your password again" required>
                    <span class="input-group-text see-pass" style="cursor: pointer;"><i class="bi bi-eye-fill"></i></span>
                </div>
                <div id="check-pass-again" class="fs-6 fst-italic text-danger" style="display: none;">Passwords do not match</div>
            </div>
            <div class="form-group mb-3">
                <label for="formImg" class="form-label">Avatar</label>
                <input class="form-control" type="file" id="formImg" accept="image/png,image/gif,image/jpeg" name="avatar">
            </div>
        `)

        $(".form-body").replaceWith(formBody)
        $("button[type='submit']").text("Register")
        $("#register").css({
            "backgroundColor": "rgb(36, 41, 102)",
            "color": "white"
        })

        tabStatus = false
    }
})

// Show the password
$(document).on("click", ".see-pass", function() {
    let currentStatus = changeEye($(this))
    let password = $(this).parent().children("input")

    if (currentStatus === "not show") {
        password.attr("type", "text")
    } else {
        password.attr("type", "password")
    }

    // Change the eye icon
    function changeEye(icon) {
        icon = icon.children().first()
        if (icon.hasClass("bi-eye-slash-fill")) {
            icon.replaceWith("<i class='bi bi-eye-fill'></i>")
            return "show"
        } else if (icon.hasClass("bi-eye-fill")) {
            icon.replaceWith("<i class='bi bi-eye-slash-fill'></i>")
            return "not show"
        }
    }
})

// Remove error message when input in LOGIN mode
$(document).on("input", "input", () => {
    $("#error-message").css({"display": "none"}).html("")
})

// Check the password in REGISTER mode
$("#register").on("click", () => {

    // Password must have at least 6 characters
    $("#pass").on("input", function() {
        if ($(this).val().length < 6) {
            $("#check-pass").css({"display": "block"})
        } else {
            $("#check-pass").css({"display": "none"})
        }
    })

    // Password must match together
    $("#pass-again").on("input", function() {
        if ($(this).val() !== $("#pass").val()) {
            $("#check-pass-again").css({"display": "block"})
        } else {
            $("#check-pass-again").css({"display": "none"})
        }
    })
})

// When the form submit
$("form").on("submit", async function(event) {
    event.preventDefault()
    console.log(this)

    // Form is valided with broswer check
    if (!this.checkValidity()) {
        this.reportValidity()
        return
    }

    // Password < 6
    if ($("#pass").val().length < 6) {
        $("#error-message").css({"display": "block"}).html("The password must have at least 6 characters")

    // Login mode
    } else if (tabStatus === true) {
        let form = new FormData(this)
        let res = await fetch("/login/login" , {
            method: "post",
            body: form
        })

        if (res.ok) {
            let result = await res.json()
            if (result["success"] === true) {
                window.location.href = "/chatapp";
            } else {
                $("#error-message").css({"display": "block"}).html(result["message"])
            }
        }

    // Register mode
    } else {
        let form = new FormData(this)
        let res = await fetch("/login/register" , {
            method: "post",
            body: form
        })

        if (res.ok) {
            let result = await res.json()
            if (result["success"] === true) {
                window.location.href = "/login";
            } else {
                $("#error-message").css({"display": "block"}).html(result["message"])
            }
        }
    }
})