services:
    mysql-server:
        image: mysql:8.4.2
        platform: linux/x86_64 # thêm lệnh này để chạy được trên Mac M1, nếu lỗi trên chip Intel thì xóa ra
        ports:
            - 3399:3306 # nếu muốn đổi thì chỉ cần đổi số 3399, không được đổi 3306
        restart: always
        volumes:
            - ./config/data:/var/lib/mysql # nếu để dòng này thì dòng bên dưới sẽ không có tác dụng
            - ./config/:/docker-entrypoint-initdb.d/
        environment:
            MYSQL_ROOT_PASSWORD: root
            MYSQL_USER: user
            MYSQL_PASSWORD: user
            
    web:
        build: ./config/
        restart: always
        ports:
            - 8080:80 # nếu muốn đổi thì chỉ được đổi số 8080
        volumes:
            - ./public:/var/www/html/
            - .:/var/www/
        depends_on:
            - "mysql-server"

    phpmyadmin:
        image: phpmyadmin/phpmyadmin:latest
        ports:
            - 8888:80
        restart: always
        environment:
            - PMA_HOST=mysql-server
        depends_on:
            - "mysql-server"
