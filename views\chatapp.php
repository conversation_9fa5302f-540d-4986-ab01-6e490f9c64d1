<?php
// if (session_status() === PHP_SESSION_NONE) {
//     session_start();
// }
// require_once("Database/Connection.php");

// if (!isset($_SESSION["userID"])) {
//     header("Location: Authentication/Form.php");
//     exit();
// }

// if (isset($_SESSION['anoUserID']) && isset($_SESSION['oldestMsgID']) && isset($_SESSION['newestMsgIDFromAno'])) {
//     $sql = "DELETE FROM chatrooms WHERE userID = ? AND connect_to = ?";
//     $stmt = $con->prepare($sql);
//     $stmt->bind_param("ss", $_SESSION["userID"], $_SESSION["anoUserID"]);
//     $stmt->execute();
//     $stmt->free_result();

//     unset($_SESSION['anoUserID']);
//     unset($_SESSION['oldestMsgID']);
//     unset($_SESSION['newestMsgIDFromAno']);
// }

// $userID = $_SESSION["userID"];

// $sql = "SELECT * FROM users WHERE userID = ?";
// $stmt = $con->prepare($sql);
// $stmt->bind_param("s", $userID);
// $stmt->execute();

// $user = $stmt->get_result()->fetch_assoc();
// $userlogo = $user["image"];
// $username = $user["username"];
// $active = $user["active"];

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js" integrity="sha384-k6d4wzSIapyDyv1kpU366/PK5hCdSbCRGRCMv+eplOQJWyd1fbcAu9OCUj5zNLiq" crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="stylesheet" href="ChatApp_Box/ChatApp.css">
    <title>Chat App</title>
</head>
<body>
    <div class="containers d-flex justify-content-center align-items-center vh-100" style="background-color: darkgrey;">
        <div id="chat-box" class="rounded bg-white p-3">
            <div class="d-flex align-items-center border-bottom pb-3">
                <img src="<?= $userlogo ?>" class="rounded-circle object-fit-cover me-3" width="60" height="60" alt="Avatar">
                <div class="d-flex flex-column">
                    <div class="fs-5"><?= $username ?></div>
                    <div class="text-success"><i class="fa-solid fa-circle text-success me-2"></i>Online</div>
                </div>
                <a class="btn btn-danger align-self-center ms-auto fs-4" href="Authentication/Logout.php" style="height: 50px;">Logout</a>
            </div>

            <div class="pt-3">
                <form action="" method="get">
                    <div class="d-flex">
                        <div class="align-self-center">Select an user to start a chat</div>
                        <span id="search" class="input-group-text ms-auto" style="height: 37.6px;cursor: pointer;"><i class="fa-solid fa-magnifying-glass"></i></span>
                    </div>
                </form>
                <ul class="list-group list-group-flush">

                </ul>
            </div>
        </div>
    </div>
    
    <script src="ChatApp_Box/ChatApp.js"></script>
</body>
</html>
