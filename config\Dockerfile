FROM php:8.4-apache 

# Cài các extension PHP cần thiết
RUN docker-php-ext-install mysqli pdo pdo_mysql && docker-php-ext-enable mysqli

# Cài các công cụ hệ thống cần cho Composer
RUN apt-get update && apt-get install -y \
    unzip \
    zip \
    curl \
    git \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Cài Composer toàn cục
RUN curl -sS https://getcomposer.org/installer | php && \
    mv composer.phar /usr/local/bin/composer && \
    chmod +x /usr/local/bin/composer