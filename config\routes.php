<?php
$routes = [
    '' => [
        'controller' => 'login',
        'method' => 'index'
    ],
    'login' => [
        'controller' => 'login',
        'method' => 'index'
    ],
    'chatapp' => [
        'controller' => 'chatapp',
        'method' => 'index'
    ],
    'logout' => [
        'controller' => 'logout',
        'method' => 'index'
    ],
    'login/login' => [
        'controller' => 'login',
        'method' => 'login'
    ],
    'login/register' => [
        'controller' => 'login',
        'method' => 'register'
    ]

];

// Function route
function route($uri, $routes) {
    $uri = !empty($_COOKIE['userID']) || $uri === '/login/login' || $uri === '/login/register' ? trim($uri, '/') : 'login';
    if (isset($routes[$uri])) {
        $controller = $routes[$uri]['controller'];
        $method = $routes[$uri]['method'];
        require_once "../controllers/{$controller}.php";
        $controller = new $controller();
        $controller->$method();
    } else {
        // Page not found
        require_once "../views/404.php";
    }
}
?>