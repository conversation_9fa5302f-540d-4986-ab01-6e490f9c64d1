SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

DROP DATABASE IF EXISTS `chatapp`;
CREATE DATABASE `chatapp` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `chatapp`;

CREATE TABLE `accounts` (
  `userID` varchar(17) NOT NULL,
  `email` varchar(50) NOT NULL UNIQUE,
  `username` varchar(32) NOT NULL,
  `password` varchar(255) NOT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `active` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`userID`)
);

CREATE TABLE `chatrooms` (
  `userID` varchar(17),
  `connect_to` varchar(17)
);

CREATE TABLE `messages` (
  `msgID` int(11) NOT NULL AUTO_INCREMENT,
  `sending_user_ID` varchar(17),
  `recieving_user_ID` varchar(17),
  `message` text NOT NULL,
  `sending_status` varchar(10) DEFAULT "sent",
  PRIMARY KEY (`msgID`)
);

INSERT INTO `accounts` (`userID`, `email`, `username`, `password`, `avatar`, `active`) VALUES
("*****************", "<EMAIL>", "an", "$2y$10$ve4EFuIiVtioHk6nonSdeOKemqmqXf9BRB/ZJZSTVjrZalrmr9eQm", "/storage/avatars/defaultImg.jpg", 0),
("*****************", "<EMAIL>", "binh", "$2y$10$ve4EFuIiVtioHk6nonSdeOKemqmqXf9BRB/ZJZSTVjrZalrmr9eQm", "/storage/avatars/defaultImg.jpg", 0),
("*****************", "<EMAIL>", "hoang", "$2y$10$ve4EFuIiVtioHk6nonSdeOKemqmqXf9BRB/ZJZSTVjrZalrmr9eQm", "/storage/avatars/defaultImg.jpg", 0);

INSERT INTO `messages` (`msgID`, `sending_user_ID`, `recieving_user_ID`, `message`, `sending_status`) VALUES
(1, "*****************", "*****************", "Hey, how are you doing?", "sent"),
(2, "*****************", "*****************", "I\'m doing great, thanks!", "sent"),
(3, "*****************", "*****************", "Hello, available to chat?", "sent"),
(4, "*****************", "*****************", "Sure, what\'s up?", "sent"),
(5, "*****************", "*****************", "Meeting at 3 PM, right?", "seen"),
(6, "*****************", "*****************", "Yes, don\'t be late!", "seen"),
(7, "*****************", "*****************", "Did you check the document?", "sent"),
(8, "*****************", "*****************", "Not yet, will do soon.", "sent"),
(9, "*****************", "*****************", "I emailed you the file.", "sent"),
(10, "*****************", "*****************", "Got it, thanks!", "sent"),
(11, "*****************", "*****************", "Remember", "seen");

ALTER TABLE `chatrooms`
  ADD FOREIGN KEY (`userID`) REFERENCES `accounts` (`userID`) ON DELETE SET NULL,
  ADD FOREIGN KEY (`connect_to`) REFERENCES `accounts` (`userID`) ON DELETE SET NULL;

ALTER TABLE `messages`
  ADD FOREIGN KEY (`sending_user_ID`) REFERENCES `accounts` (`userID`) ON DELETE SET NULL,
  ADD FOREIGN KEY (`recieving_user_ID`) REFERENCES `accounts` (`userID`) ON DELETE SET NULL;
COMMIT;
