Set up at server side (for file .htaccess in docker) - run all commands below via cmd:
1. docker-compose ps (to get the container name)
2. docker exec -it <container_name> <command> (command: bash)
3. a2enmod rewrite
4. service apache2 restart
5. apt update && apt install nano -y (if nano is not installed)
6. nano /etc/apache2/sites-available/000-default.conf
7. Add the following lines to the file:
    <VirtualHost *:80>
        ServerAdmin webmaster@localhost
        DocumentRoot /var/www/html

        <Directory /var/www/html>
            Options Indexes FollowSymLinks
            AllowOverride All
            Require all granted
        </Directory>

        ErrorLog ${APACHE_LOG_DIR}/error.log
        CustomLog ${APACHE_LOG_DIR}/access.log combined
    </VirtualHost>
8. close nano (ctrl + o, enter, ctrl + x)
9. service apache2 restart
10. create .htaccess file in /var/www/html/public with the following content:
    RewriteEngine On

    # Nếu file tồn tại thì không rewrite
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d

    # <PERSON><PERSON><PERSON> mọi request về index.php, phần còn lại sẽ nằm trong $_SERVER['REQUEST_URI']
    RewriteRule ^(.*)$ index.php [QSA,L]
