<?php
Class LoginModel {
    private $con;

    public function __construct() {
        require_once 'connection.php';
        $this->con = $con;
    }

    public function login($email, $password) {
        $sql = "SELECT * FROM accounts WHERE email = ?";
        $stmt = $this->con->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            return ["success" => false, "message" => "Email or password is incorrect"];
        } else if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            if (password_verify($password, $row['password'])) {
                setcookie('userID', $row['userID'], time() + 3600, '/');
                return ["success" => true];
            } else {
                return ["success" => false, "message" => "Email or password is incorrect"];
            }
        }
    }

    function generateUserID() {
        $timestamp = date('YmdHis');
        $rand = rand(100, 999);
        return $timestamp . $rand;
    }

    function saveAvatar($avatar) {
        // Image handle
        $avatarDir = "/storage/avatars/";
        $avatarPath = "";
        $allowType = ["image/png", "image/gif", "image/jpeg"];
        $maxSize = 5 * 1024 * 1024;

        // Upload image file
        if (!empty($avatar["name"])) {

            if ($avatar["error"] !== 0) {
                return ["success"=> false, "message"=> 'Upload image fail. Please try again!'];
            }
            if (!in_array($avatar["type"], $allowType)) {
                return ["success"=> false, "message"=> "Invalid file type. Only PNG, GIF, JPEG, JPG allowed!"];
            }
            if ($avatar["size"] > $maxSize) {
                return ["success"=> false, "message"=> "File size exceeds 5MB!"];
            }

            $avatarExt = pathinfo($avatar["name"], PATHINFO_EXTENSION);
            $avatarName = "image_" . time() . "." . $avatarExt;
            $avatarPath = $avatarDir . $avatarName;

            if (!move_uploaded_file($avatar["tmp_name"], $avatarPath)) {
                return ["success"=> false, "message"=> "Save image fail. Please try again!"];
            }

        } else {
            $avatarPath = $avatarDir . "defaultImg.jpg";
        }
        return ["success"=> true, "path"=> $avatarPath];
    }

    public function register($email, $username, $password, $avatar) {
        $userID = $this->generateUserID();
        $password = password_hash($password, PASSWORD_DEFAULT);

        $saveResult = $this->saveAvatar($avatar);
        if ($saveResult["success"] === false) {
            return ["success" => false, "message" => $saveResult["message"]];
        } else {
            $avatarPath = $saveResult["path"];
        }

        $sql = "SELECT * FROM accounts WHERE email = ?";
        $stmt = $this->con->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            return ["success" => false, "message" => "Email already exists"];
        } else {
            $sql = "INSERT INTO `accounts` (`userID`, `email`, `username`, `password`, `avatar`) VALUES (?, ?, ?, ?, ?)";
            $stmt = $this->con->prepare($sql);
            $stmt->bind_param("sssss", $userID, $email, $username, $password, $avatarPath);
            $stmt->execute();
            return ["success" => true];
        }
    }
}
?>